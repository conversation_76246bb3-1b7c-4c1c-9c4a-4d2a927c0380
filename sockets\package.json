{"name": "mystique-socket-messages", "version": "1.0.0", "description": "Socket.IO message server for Mystique application", "main": "message.ts", "scripts": {"start": "tsx message.ts", "dev": "tsx watch message.ts", "build": "tsc message.ts --outDir dist", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"socket.io": "^4.8.1", "dotenv": "^17.2.2"}, "devDependencies": {"@types/node": "^20", "tsx": "^4.20.5", "typescript": "^5"}, "keywords": ["socket.io", "websocket", "real-time", "messaging"], "author": "", "license": "ISC"}