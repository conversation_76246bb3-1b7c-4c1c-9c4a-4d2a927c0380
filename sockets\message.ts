import { Server } from "socket.io";
import { createServer } from "http";
import { config } from "dotenv";

// Load environment variables
config();

const PORT = process.env.SOCKET_PORT || 3001;

// Create HTTP server
const httpServer = createServer();

// Create Socket.IO server with CORS configuration
const io = new Server(httpServer, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"],
    credentials: true,
  },
});

// Interface for message data
interface MessageData {
  id: string;
  content: string;
  userId: string;
  username: string;
  timestamp: Date;
  roomId?: string;
}

// Interface for user data
interface UserData {
  userId: string;
  username: string;
  roomId?: string;
}

// Store connected users
const connectedUsers = new Map<string, UserData>();

// Socket connection handler
io.on("connection", (socket) => {
  console.log(`User connected: ${socket.id}`);

  // Handle user joining
  socket.on("join", (userData: UserData) => {
    connectedUsers.set(socket.id, userData);

    // Join room if specified
    if (userData.roomId) {
      socket.join(userData.roomId);
      socket.to(userData.roomId).emit("user_joined", {
        userId: userData.userId,
        username: userData.username,
        message: `${userData.username} joined the room`,
      });
    }

    console.log(`User ${userData.username} (${userData.userId}) joined`);
  });

  // Handle sending messages
  socket.on("send_message", (messageData: MessageData) => {
    const user = connectedUsers.get(socket.id);

    if (!user) {
      socket.emit("error", { message: "User not authenticated" });
      return;
    }

    // Add timestamp and user info to message
    const completeMessage: MessageData = {
      ...messageData,
      userId: user.userId,
      username: user.username,
      timestamp: new Date(),
    };

    // Send to specific room or broadcast to all
    if (messageData.roomId) {
      socket.to(messageData.roomId).emit("receive_message", completeMessage);
    } else {
      socket.broadcast.emit("receive_message", completeMessage);
    }

    // Send confirmation back to sender
    socket.emit("message_sent", completeMessage);

    console.log(`Message from ${user.username}: ${messageData.content}`);
  });

  // Handle joining a room
  socket.on("join_room", (roomId: string) => {
    const user = connectedUsers.get(socket.id);

    if (!user) {
      socket.emit("error", { message: "User not authenticated" });
      return;
    }

    // Leave previous room if any
    if (user.roomId) {
      socket.leave(user.roomId);
      socket.to(user.roomId).emit("user_left", {
        userId: user.userId,
        username: user.username,
        message: `${user.username} left the room`,
      });
    }

    // Join new room
    socket.join(roomId);
    user.roomId = roomId;
    connectedUsers.set(socket.id, user);

    socket.to(roomId).emit("user_joined", {
      userId: user.userId,
      username: user.username,
      message: `${user.username} joined the room`,
    });

    socket.emit("room_joined", { roomId });
    console.log(`User ${user.username} joined room: ${roomId}`);
  });

  // Handle leaving a room
  socket.on("leave_room", () => {
    const user = connectedUsers.get(socket.id);

    if (!user || !user.roomId) {
      return;
    }

    socket.leave(user.roomId);
    socket.to(user.roomId).emit("user_left", {
      userId: user.userId,
      username: user.username,
      message: `${user.username} left the room`,
    });

    user.roomId = undefined;
    connectedUsers.set(socket.id, user);

    socket.emit("room_left");
    console.log(`User ${user.username} left the room`);
  });

  // Handle typing indicators
  socket.on("typing_start", (roomId?: string) => {
    const user = connectedUsers.get(socket.id);

    if (!user) return;

    const typingData = {
      userId: user.userId,
      username: user.username,
    };

    if (roomId) {
      socket.to(roomId).emit("user_typing", typingData);
    } else {
      socket.broadcast.emit("user_typing", typingData);
    }
  });

  socket.on("typing_stop", (roomId?: string) => {
    const user = connectedUsers.get(socket.id);

    if (!user) return;

    const typingData = {
      userId: user.userId,
      username: user.username,
    };

    if (roomId) {
      socket.to(roomId).emit("user_stopped_typing", typingData);
    } else {
      socket.broadcast.emit("user_stopped_typing", typingData);
    }
  });

  // Handle disconnection
  socket.on("disconnect", () => {
    const user = connectedUsers.get(socket.id);

    if (user) {
      // Notify room if user was in one
      if (user.roomId) {
        socket.to(user.roomId).emit("user_left", {
          userId: user.userId,
          username: user.username,
          message: `${user.username} disconnected`,
        });
      }

      connectedUsers.delete(socket.id);
      console.log(`User ${user.username} (${user.userId}) disconnected`);
    } else {
      console.log(`User disconnected: ${socket.id}`);
    }
  });
});

// Start the server
httpServer.listen(PORT, () => {
  console.log(`Socket.IO message server running on port ${PORT}`);
});

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received, shutting down gracefully");
  httpServer.close(() => {
    console.log("Socket.IO server closed");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("SIGINT received, shutting down gracefully");
  httpServer.close(() => {
    console.log("Socket.IO server closed");
    process.exit(0);
  });
});

export default io;
