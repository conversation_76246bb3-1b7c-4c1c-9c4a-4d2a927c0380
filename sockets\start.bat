@echo off

REM Socket.IO Message Server Start Script for Windows

echo Starting Socket.IO Message Server...

REM Check if tsx is installed globally
tsx --version >nul 2>&1
if %errorlevel% neq 0 (
    echo tsx not found globally, installing...
    npm install -g tsx
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
)

REM Start the server
echo Starting server on port %SOCKET_PORT%...
if "%SOCKET_PORT%"=="" set SOCKET_PORT=3001
echo Server will run on port %SOCKET_PORT%
tsx message.ts
