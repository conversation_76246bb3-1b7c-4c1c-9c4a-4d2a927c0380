#!/bin/bash

# Socket.IO Message Server Start Script

echo "Starting Socket.IO Message Server..."

# Check if tsx is installed globally
if ! command -v tsx &> /dev/null; then
    echo "tsx not found globally, installing..."
    npm install -g tsx
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

# Start the server
echo "Starting server on port ${SOCKET_PORT:-3001}..."
tsx message.ts
