# Use Node.js Alpine image for smaller size
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Copy main package files first for better caching
COPY package*.json ./

# Install main dependencies
RUN npm ci --only=production

# Copy socket package files
COPY sockets/package*.json ./sockets/

# Install socket-specific dependencies
WORKDIR /app/sockets
RUN npm ci --only=production

# Copy socket files and configs
COPY sockets/ ./
COPY tsconfig.json ../

# Install TypeScript globally for running ts files
RUN npm install -g tsx

# Expose the socket port
EXPOSE 3001

# Start the socket server
CMD ["tsx", "message.ts"]
